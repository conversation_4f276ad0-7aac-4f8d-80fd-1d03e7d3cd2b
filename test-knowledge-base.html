<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库左侧菜单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
            background-color: #f1f8e9;
        }
        .test-data {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>知识库左侧菜单默认选中测试</h1>
        
        <h2>测试说明</h2>
        <p>此测试验证知识库页面左侧树图是否能正确默认选中第一条能选中的数据（即第一个三级节点）。</p>
        
        <h2>修改内容</h2>
        <ul>
            <li>在 <code>leftMenu.vue</code> 组件中添加了 <code>menuData</code> 的 watch 监听器</li>
            <li>添加了 <code>selectFirstAvailableNode()</code> 方法来选中第一个可选中的节点</li>
            <li>添加了 <code>findFirstSelectableNode()</code> 方法来递归查找第一个三级节点</li>
            <li>修改了 <code>mounted</code> 生命周期，在没有指定激活节点时自动选中第一个可选中的节点</li>
        </ul>
        
        <h2>预期行为</h2>
        <div class="test-data">
1. 页面加载时，知识库页面调用 getClassifyDataFn() 获取菜单数据
2. menuData 更新后，leftMenu 组件的 watch 监听器被触发
3. 如果没有 activeItemId，自动调用 selectFirstAvailableNode()
4. 查找第一个 level === 3 的节点并选中
5. 展开该节点的所有父节点
6. 触发 item-click 事件通知父组件
7. 右侧显示对应的知识详情
        </div>
        
        <h2>测试步骤</h2>
        <ol>
            <li>打开知识库页面 (<code>/serve/knowledgeBase</code>)</li>
            <li>观察左侧树图是否自动选中了第一个可选中的节点</li>
            <li>检查右侧是否显示了对应的知识详情</li>
            <li>验证选中的节点是否为第一个三级节点</li>
        </ol>
        
        <h2>代码变更位置</h2>
        <div class="test-data">
文件: src/views/serve/knowledgeBase/components/leftMenu.vue

新增的 watch 监听器:
- menuData: 监听菜单数据变化，自动选中第一个可选中的节点

新增的方法:
- selectFirstAvailableNode(): 选中第一个可选中的节点
- findFirstSelectableNode(): 递归查找第一个三级节点

修改的方法:
- mounted(): 在组件挂载时也触发默认选中逻辑
        </div>
        
        <div class="test-result">
            <strong>✅ 修改完成</strong><br>
            左侧树图现在会在数据加载完成后自动选中第一个可选中的节点（三级节点），并展开相应的父节点，同时触发右侧内容的显示。
        </div>
    </div>
</body>
</html>
